package hk.org.ha.sc3.sybasechatops;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpSession;

import hk.org.ha.sc3.sybasechatops.controller.SessionController;
import hk.org.ha.sc3.sybasechatops.model.NewSessionRequest;
import hk.org.ha.sc3.sybasechatops.model.NewSessionResponse;

@SpringBootTest
public class SessionControllerTest {

    @Autowired
    private SessionController sessionController;

    @Test
    public void testCreateNewSession() {
        // Create a new session request with sample data
        NewSessionRequest request = new NewSessionRequest();
        request.setRequesterId("testUser123");
        request.setRoomId("testRoom456");
        request.setDbType("ORACLE");
        request.setTeam("SC3_TEAM");
        request.setProduct("CHATOPS");
        request.setCmdGrpId("ORACLE_HEALTH_CHECK");

        // Create a mock HTTP session
        MockHttpSession mockSession = new MockHttpSession();

        // Call the controller method
        NewSessionResponse response = sessionController.createNewSession(request, mockSession);

        // Verify the response
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());
        assertEquals("New session created successfully", response.getMessage());
        assertNotNull(response.getSessionId());
        assertEquals("testUser123", response.getRequesterId());
        assertEquals("testRoom456", response.getRoomId());
        assertEquals("ORACLE", response.getDbType());
        assertEquals("SC3_TEAM", response.getTeam());
        assertEquals("CHATOPS", response.getProduct());
        assertEquals("ORACLE_HEALTH_CHECK", response.getCmdGrpId());

        // Verify session attributes were set
        assertEquals("testUser123", mockSession.getAttribute("REQUESTER_ID"));
        assertEquals("testRoom456", mockSession.getAttribute("ROOM_ID"));
        assertEquals("ORACLE", mockSession.getAttribute("DB_TYPE"));
        assertEquals("SC3_TEAM", mockSession.getAttribute("TEAM"));
        assertEquals("CHATOPS", mockSession.getAttribute("PRODUCT"));
        assertEquals("ORACLE_HEALTH_CHECK", mockSession.getAttribute("CMD_GRP_ID"));
    }

    @Test
    public void testCreateNewSessionWithMinimalData() {
        // Create a new session request with minimal data
        NewSessionRequest request = new NewSessionRequest();
        request.setRequesterId("minimalUser");
        request.setRoomId("minimalRoom");

        // Create a mock HTTP session
        MockHttpSession mockSession = new MockHttpSession();

        // Call the controller method
        NewSessionResponse response = sessionController.createNewSession(request, mockSession);

        // Verify the response
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());
        assertEquals("New session created successfully", response.getMessage());
        assertNotNull(response.getSessionId());
        assertEquals("minimalUser", response.getRequesterId());
        assertEquals("minimalRoom", response.getRoomId());

        // Verify session attributes were set
        assertEquals("minimalUser", mockSession.getAttribute("REQUESTER_ID"));
        assertEquals("minimalRoom", mockSession.getAttribute("ROOM_ID"));
    }

    @Test
    public void testCreateNewSessionWithEmptyRequest() {
        // Create an empty session request
        NewSessionRequest request = new NewSessionRequest();

        // Create a mock HTTP session
        MockHttpSession mockSession = new MockHttpSession();

        // Call the controller method
        NewSessionResponse response = sessionController.createNewSession(request, mockSession);

        // Verify the response
        assertNotNull(response);
        assertEquals(200, response.getStatusCode());
        assertEquals("New session created successfully", response.getMessage());
        assertNotNull(response.getSessionId());
    }
}
