meta {
  name: Create New Session
  type: http
  seq: 1
}

post {
  url: http://localhost:8080/chatops/session/new
  body: json
  auth: none
}

body:json {
  {
    "requesterId": "testUser123",
    "roomId": "testRoom456",
    "dbType": "ORACLE",
    "team": "SC3_TEAM",
    "product": "CHATOPS",
    "cmdGrpId": "ORACLE_HEALTH_CHECK"
  }
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains session ID", function() {
    expect(res.getBody().sessionId).to.be.a('string');
    expect(res.getBody().sessionId.length).to.be.greaterThan(0);
  });
  
  test("Response contains correct data", function() {
    expect(res.getBody().requesterId).to.equal("testUser123");
    expect(res.getBody().roomId).to.equal("testRoom456");
    expect(res.getBody().dbType).to.equal("ORACLE");
    expect(res.getBody().team).to.equal("SC3_TEAM");
    expect(res.getBody().product).to.equal("CHATOPS");
    expect(res.getBody().cmdGrpId).to.equal("ORACLE_HEALTH_CHECK");
  });
  
  test("Status code is success", function() {
    expect(res.getBody().statusCode).to.equal(200);
  });
  
  test("Success message", function() {
    expect(res.getBody().message).to.equal("New session created successfully");
  });
}
