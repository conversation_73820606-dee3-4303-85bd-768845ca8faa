meta {
  name: Create Minimal Session
  type: http
  seq: 2
}

post {
  url: http://localhost:8080/chatops/session/new
  body: json
  auth: none
}

body:json {
  {
    "requesterId": "minimalUser",
    "roomId": "minimalRoom"
  }
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response contains session ID", function() {
    expect(res.getBody().sessionId).to.be.a('string');
    expect(res.getBody().sessionId.length).to.be.greaterThan(0);
  });
  
  test("Response contains minimal data", function() {
    expect(res.getBody().requesterId).to.equal("minimalUser");
    expect(res.getBody().roomId).to.equal("minimalRoom");
  });
}
