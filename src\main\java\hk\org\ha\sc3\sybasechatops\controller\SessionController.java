package hk.org.ha.sc3.sybasechatops.controller;

import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.SessionAttrEnum;
import hk.org.ha.sc3.sybasechatops.model.NewSessionRequest;
import hk.org.ha.sc3.sybasechatops.model.NewSessionResponse;

@RestController
@RequestMapping("session")
public class SessionController {

    private static final Logger log = LoggerFactory.getLogger(SessionController.class);

    @PostMapping("new")
    public NewSessionResponse createNewSession(@RequestBody NewSessionRequest request, HttpSession httpSession) {
        log.debug("Creating new session with request: {}", request);
        
        // Get the session ID (Spring will create a new session if one doesn't exist)
        String sessionId = httpSession.getId();
        log.debug("New session created with ID: {}", sessionId);

        // Set session attributes if provided in the request
        if (request.getRequesterId() != null) {
            httpSession.setAttribute(SessionAttrEnum.REQUESTER_ID.name(), request.getRequesterId());
            log.debug("Set REQUESTER_ID: {}", request.getRequesterId());
        }

        if (request.getRoomId() != null) {
            httpSession.setAttribute(SessionAttrEnum.ROOM_ID.name(), request.getRoomId());
            log.debug("Set ROOM_ID: {}", request.getRoomId());
        }

        if (request.getDbType() != null) {
            httpSession.setAttribute(SessionAttrEnum.DB_TYPE.name(), request.getDbType());
            log.debug("Set DB_TYPE: {}", request.getDbType());
        }

        if (request.getTeam() != null) {
            httpSession.setAttribute(SessionAttrEnum.TEAM.name(), request.getTeam());
            log.debug("Set TEAM: {}", request.getTeam());
        }

        if (request.getProduct() != null) {
            httpSession.setAttribute(SessionAttrEnum.PRODUCT.name(), request.getProduct());
            log.debug("Set PRODUCT: {}", request.getProduct());
        }

        if (request.getCmdGrpId() != null) {
            httpSession.setAttribute(SessionAttrEnum.CMD_GRP_ID.name(), request.getCmdGrpId());
            log.debug("Set CMD_GRP_ID: {}", request.getCmdGrpId());
        }

        // Build and return the response
        return NewSessionResponse.builder()
                .sessionId(sessionId)
                .message("New session created successfully")
                .statusCode(200)
                .requesterId(request.getRequesterId())
                .roomId(request.getRoomId())
                .dbType(request.getDbType())
                .team(request.getTeam())
                .product(request.getProduct())
                .cmdGrpId(request.getCmdGrpId())
                .build();
    }
}
